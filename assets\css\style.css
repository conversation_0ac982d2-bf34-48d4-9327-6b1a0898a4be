@import url("https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i&display=swap");
body {
    /*font-family: 'Varela Round', sans-serif;*/
    font-family: "Poppins", sans-serif;
    position: relative;
    font-size: 14px;
    color: #4e4e4e;
    overflow-x: hidden;
    letter-spacing: 0px;
}

h1,
h2,
h2,
h3,
h4,
h5,
h6,
li,
a {
    /*font-family: 'Varela Round', sans-serif;*/
    font-family: "Poppins", sans-serif;
    letter-spacing: 0px;
}

.tap-top {
    background: #01B0B8 !important;
}

.copyright.agency.inner-page {
    background: #1D456B !important;
}

.loding-header {
    background-color: #133351 !important;
    padding: 20px 5px;
}

.bg.footer2 {
    background: #133351 !important;
}

.copyright-text {
    color: white;
}

.h4-format {
    margin-bottom: 10px !important;
}

.primary-btn {
    background: #01B0B8 !important;
    border-radius: 80px;
}

input.field-bg {
    background-color: white;
}

.logo-header {
    max-width: 150px;
    height: auto;
}

.logo-footer {
    max-width: 80px !important;
    height: auto;
    margin-bottom: 20px;
}

a.email {
    color: #007AFF;
}

.left-text {
    text-align: left !important;
}

div.link-horizontal {
    text-align: center !important;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

section.p-50 {
    padding: 50px;
}

.link-horizontal ul li a.icon-btn i {
    color: #133351;
    background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(50%, transparent), color-stop(50%, #133351), to(#133351)) !important;
    background-image: linear-gradient(to bottom, transparent 0%, transparent 50%, #133351 50%, #133351 100%) !important;
}

h2,
h3,
h4 {
    color: #133351;
}

.font-primary {
    color: #133351 !important;
}

.services .service.service-overlay {
    transition: none;
    -webkit-transition: none;
    transform: none !important;
    will-change: none !important;
    box-shadow: 0px 0px 20px #00000029;
}

.services .service.service-overlay:hover img {
    /*-webkit-filter: brightness(0) invert(1);
    filter: brightness(0) invert(1);*/
    -webkit-filter: none;
    filter: none;
}

.service-img {
    height: 100px;
}

.services .service {
    padding: 60px 25px 50px 25px;
}

.services .service.service-overlay:hover {
    /*background: -webkit-gradient(linear, left top, left bottom, from(#133351), to(#01B0B8));
    background: linear-gradient(#133351, #01B0B8);*/
    background: unset;
    box-shadow: 0px 0px 20px #00000029;
}

.services .service.service-overlay:hover .service-feature .feature-text,
.services .service.service-overlay:hover .service-feature p {
    color: black;
}

.services .service.service-overlay .service-feature .feature-text,
.services .service.service-overlay .service-feature p {
    color: black;
}

.services .service:hover .img-block {
    /*-webkit-animation: scaleDisappear 0.3s;
    animation: scaleDisappear 0.3s;*/
    -webkit-animation: none;
    animation: none;
}

.bg-theme {
    /*background: -webkit-gradient(linear, left top, left bottom, from(#133351), to(#01B0B8));
    background: linear-gradient(#133351, #01B0B8);*/
    background: transparent linear-gradient(0, #031C33 0%, #01B0B8 100%) 0% 0% no-repeat padding-box;
}

.bg-orange {
    background-color: #FFEECF;
}

.bg-dark-orange {
    background-color: #ff7d00;
}

.rounded-1 {
    border-radius: 1rem;
}

.icon-thumb {
    width: 40%;
    max-width: 100px;
}

.larg-icon-thumb {
    height: 150px;
}

.larg-icon-thumb>img {
    width: 70%;
    max-width: 130px;
}

.bg-pearl-theme {
    background: transparent linear-gradient(180deg, #FFA900 0%, #182274 100%) 0% 0% no-repeat padding-box;
}

.bg-instacare-theme {
    background: transparent linear-gradient(180deg, #03B0B8 0%, #8556D5 100%) 0% 0% no-repeat padding-box;
}

.bg-instacare-theme1 {
    background: transparent linear-gradient(270deg, #8557D636 0%, #FFFFFF1F 100%) 0% 0% no-repeat padding-box;
}

.bg-instacare-theme2 {
    background: transparent linear-gradient(90deg, #8557D636 0%, #FFFFFF1F 100%) 0% 0% no-repeat padding-box;
}

.bg-instacare-theme3 {
    background: transparent linear-gradient(0deg, #03B0B8 0%, #8556D5 100%) 0% 0% no-repeat padding-box;
}

.bg-instacare-theme4 {
    background: transparent linear-gradient(180deg, #857998 0%, #FFFFFF 100%) 0% 0% no-repeat padding-box;
}

.pearl-inspire-subscribe-button {
    background-color: #FFA900 !important;
    background-image: linear-gradient(to right, #FFA900, #F9B54C, #F9B54C, #FFA900) !important;
    border: 1px solid #ffa900 !important;
    font-size: calc(14px + (14 - 13) * ((100vw - 300px) / (1920 - 300))) !important;
    padding: 20px 30px !important;
    letter-spacing: 0 !important;
    margin-right: 15px;
}

.pearl-inspire-subscribe-icon {
    font-size: 30px !important;
    position: absolute;
    left: 50px;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    color: #FFA900;
}

.pearl-inspire-footer-bg {
    background: url("../images/pearl-inspire/l-2-footer.webp") no-repeat top;
}

.pearl-inspire-subscribe-input {
    padding-left: 110px !important;
    font-size: calc(14px + (14 - 13) * ((100vw - 300px) / (1920 - 300))) !important;
}

.pearl-inspire-subscribe-input::placeholder {
    color: #fd6d64 !important;
    opacity: 0.5;
}

.bg-pearl-theme4 {
    background: transparent linear-gradient(180deg, #9B8761 0%, #FFFFFF 100%) 0% 0% no-repeat padding-box;
}

.bg-pearl-services4 {
    background: transparent linear-gradient(0deg, #FFFFFF 0%, #162073 100%) 0% 0% no-repeat padding-box;
}

.bg-pearl-services4-item {
    border-radius: 20px;
    box-shadow: 0px 0px 20px #1C2677;
    min-height: 485px;
}

.bg-pearl-services4-item>img {
    max-width: 60%;
}

.bg-instacare-theme4 h4,
.bg-pearl-theme4 h4 {
    line-height: 1.6;
    font-size: 25px;
    color: black;
    text-transform: none;
}

.bg-instacare-theme4 a,
.bg-pearl-theme4 a {
    line-height: 1.6;
    font-size: 25px;
    color: black;
    text-transform: none;
    text-decoration: underline;
}

.img-round-border {
    border-radius: 20px;
    min-height: 425px;
}

.bg-instacare-theme3>img {
    height: 200px;
}

.bg-instacare-theme3>h2 {
    font-size: 20px;
}

.btn-insta-service-default {
    background: white;
    font-size: 20px;
    color: #18B2C9 !important;
    border: 2px solid #18B2C9;
    padding: 15px 35px 15px 35px;
    margin-bottom: 10px;
    border-radius: 25px;
}

.btn-insta-service-default:hover,
.btn-insta-service-default:focus,
.btn-insta-service-default:active {
    -webkit-transition: 0.5s;
    color: white !important;
    transition: 0.5s;
    background-color: #18B2C9;
    margin-bottom: 0px;
    margin-top: 10px;
}

.btn-default.btn-gradient {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#133351), to(#01B0B8));
    background-image: linear-gradient(#133351, #01B0B8);
    background-image: -webkit-gradient(linear, left top, right top, from(#133351), color-stop(#01B0B8), color-stop(#01B0B8), to(#133351));
    background-image: linear-gradient(to right, #133351, #01B0B8, #01B0B8, #133351);
    border: 1px solid #01B0B8 !important;
}

.socials-lists ul li a i {
    background-color: #133351;
}

.copyright {
    background: #133351;
}

.theme-pannel-main {
    display: none;
}

.videos .video-description p {
    color: #212529 !important;
}

.screenshot .swiper-container .swiper-wrapper .swiper-slide img {
    border-radius: 25px;
}

.download .information p {
    margin-top: 35px;
    margin-bottom: 25px;
}

.about.app1 .mobile.mobile-img .mobile-screen {
    height: 700px;
}

@media only screen and (max-width: 1600px) {
    .about.app1 .mobile.mobile-img .mobile-screen {
        height: 700px;
    }
}

@media only screen and (max-width: 1366px) {
    .about.app1 .mobile.mobile-img .mobile-screen {
        height: 580px;
    }
}

.logo-header {
    max-width: 75px;
    height: auto;
}


/*ul {
  list-style: none;
}*/

.gym li {
    line-height: 1.8;
    /*display: flex;*/
}


/*.gym li:before {
    content: '✓ ';
}*/

.txtShadow {
    text-shadow: 2px 2px #133351;
}

.txtShadow2 {
    text-shadow: 1px 1px #133351;
}

.socials-lists ul li a:hover i {
    background-color: #fff;
    color: #133351 !important;
    border: 1px solid #133351 !important;
}

.header .bg.app1-header {
    background: transparent linear-gradient(180deg, #031C33 0%, #024F60 15%, #025E6E 31%, #01929D 60%, #01A7B0 84%, #01AEB6 93%, #01B0B8 100%) 0% 0% no-repeat padding-box;
    height: auto;
}

.header .bg.app1-instacare-header {
    background: transparent linear-gradient(180deg, #8556D5 0%, #03B0B8 100%) 0% 0% no-repeat padding-box;
    height: auto;
}

.header .bg.app1-pearl-inspire-header {
    background: transparent linear-gradient(180deg, #FFA900 0%, #01147E 100%) 0% 0% no-repeat padding-box;
    height: auto;
}

.app1-instacare-header h1 {
    line-height: 2;
}

.app1-pearl-inspire-header h1 {
    line-height: 2;
}

.center-vertical {
    margin-top: auto;
    margin-bottom: auto;
}

.google-play-btn>img,
.app-store-btn>img {
    width: 145px;
    border-radius: 8px;
    display: inline-block;
}

.google-play-btn>img:hover,
.app-store-btn>img:hover {
    box-shadow: black -1px 2px 5px;
}

.header-text {
    white-space: nowrap;
}

.dnl-h1 {
    color: black;
}

.dnl-h3 {
    font-size: 18px;
}

.center-instacare-text {
    margin-top: 200px;
}

@media only screen and (max-width: 767px) {
    .header .bg.app1-header {
        /*background-image: url(../images/app_landing1/header-1-bg1.webp) !important;*/
        overflow: hidden;
    }
    .header .center-text {
        padding: 60px !important;
    }
    input[type=checkbox] {
        -ms-transform: scale(1);
        /* IE */
        -moz-transform: scale(1);
        /* FF */
        -webkit-transform: scale(1);
        /* Safari and Chrome */
        -o-transform: scale(1);
        /* Opera */
        padding: 0px;
    }

    .icon-thumb {
        width: 18%;
        max-width: 100px;
    }
}

@media only screen and (max-width: 991px) {
    .header .bg.app1-header {
        /*background-image: url(../images/app_landing1/header-1-bg1.webp) !important;*/
        overflow: hidden;
    }
    .header .center-text {
        padding: 60px !important;
    }
    input[type=checkbox] {
        -ms-transform: scale(1);
        /* IE */
        -moz-transform: scale(1);
        /* FF */
        -webkit-transform: scale(1);
        /* Safari and Chrome */
        -o-transform: scale(1);
        /* Opera */
        padding: 0px;
    }
}

@media only screen and (max-width: 1366px) {
    .header.app1 h1 {
        margin-top: 10px !important;
        line-height: 40px !important;
        font-size: 30px !important;
    }
    .header .header-sub-text p {
        font-size: 18px !important;
        font-size: 25px !important;
    }
    .header .header-sub-text p {
        font-size: 20px !important;
        line-height: 30px !important;
    }
    .header .bg.app1-header {
        /*background-image: url(../images/app_landing1/header-1-bg1.webp) !important;*/
        overflow: hidden;
    }
    .header .center-text {
        padding: 60px !important;
    }
}

@media only screen and (max-width: 1024px) {
    .header.app1 h1 {
        line-height: 40px !important;
        font-size: 30px !important;
    }
    .header .header-sub-text p {
        font-size: 20px !important;
        line-height: 30px !important;
    }
    .header .bg.app1-header {
        /*background-image: url(../images/app_landing1/header-1-bg1.webp) !important;*/
        height:950px !important;
        overflow: hidden;
    }
    .header .center-text {
        padding: 20px !important;
    }
    .header .center-text {
        height: 1000px !important;
    }
    .logo-header {
        margin: 30px !important;
    }
}

.services .service {
    min-height: 373px !important;
}

.congratulation-font {
    font-size: 210% !important;
}

img.congratulation {
    max-width: 300px;
    margin-bottom: 20px;
}

.center {
    text-align: center;
}

p.middle-text {
    font-size: 150% !important;
    font-weight: 500 !important;
}

.agency a.email {
    text-transform: none !important;
    letter-spacing: 1px;
}

a.back-transparent {
    border: 1px solid #01B0B8;
    background-color: #fff;
    color: #01B0B8 !important;
}

a.back-transparent:hover {
    border: 1px solid #01B0B8;
    background-color: #f9f9f9 !important;
    color: #01B0B8 !important;
}

.btn-width {
    width: 100px;
}

.gym .btn.small {
    padding: 10px;
}

label.survey {
    display: inline !important;
}

.dropdown-menu {
    max-width: 280px;
}

header.nav-abs>.container {
    max-width: 100%;
    margin-top: 20px;
}

.dropdown-item {
    padding: 4px 12px;
}

.dropdown-item>a {
    min-width: unset;
    padding: 10px;
}

.dropdown-item>a:hover {
    color: #00afb7 !important;
    transform: unset;
}

.dropdown-item.active {
    background-color: white;
}

.dropdown-item.active>a {
    color: #019aa4 !important;
}

.app1-header-divider {
    overflow: hidden;
    position: relative;
    top: 6px;
}

.app1-instacare-header-divider {
    overflow: hidden;
    position: relative;
    top: 6px;
}

.mean-bar,
.mean-nav {
    background: none !important;
}

.mean-container a.meanmenu-reveal {
    top: 20px;
    right: 20px;
}

.mean-container .mean-nav ul li a.mean-expand {
    border: unset !important;
    height: unset;
    background: unset;
    color: white;
}

.mean-container .mean-nav ul li a.mean-expand:hover {
    background: rgba(255, 255, 255, .1);
}

.mean-menu-logo {
    display: none;
}

.header-home-img {
    width: 55%;
    position: absolute;
    top: 85px;
    right: 0;
}

.service-button {
    margin-top: 30px;
}

.service-button>.btn-default1 {
    background: transparent linear-gradient(270deg, #01B0B8 0%, #133351 100%) 0% 0% no-repeat padding-box;
    padding: 10px;
    color: #fff !important;
    margin-top: 30px;
    border-radius: 5px;
}

.service-button>.btn-default1:hover,
.service-button>.btn-default1:focus,
.service-button>.btn-default1:active {
    background: transparent linear-gradient(270deg, #01B0B8 0%, #133351 100%) 0% 0% no-repeat padding-box;
    color: #fff !important;
    box-shadow: 0px 0px 10px #11415DD5;
    transition: 0.5s;
}

.button-letstalk>.btn-default {
    color: black !important;
    border: 2px solid #fff;
    font-size: large;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    background-color: #fff;
    border: 2px solid #fff;
    color: #000 !important;
    padding: 14px 10px 14px 60px;
}

.main-title>h1 {
    color: black;
}

.about-section {
    padding: 50px 0;
}

.about-section .btn-default,
.our-products .btn-default {
    background: white;
    font-size: 20px;
    color: black !important;
    border: 2px solid #fff;
    padding: 10px 35px 10px 35px;
    margin-bottom: 10px;
}

.about-section .btn-default:hover,
.about-section .btn-default:focus,
.about-section .btn-default:active,
.our-products .btn-default:hover,
.our-products .btn-default:focus,
.our-products .btn-default:active {
    -webkit-transition: 0.5s;
    transition: 0.5s;
    background-color: #fff;
    padding: 10px 35px 10px 35px;
    margin-bottom: 0px;
    margin-top: 10px;
}

.our-products .product {
    padding: 60px 40px;
    border-radius: 30px;
    text-align: center;
    min-height: 500px;
    margin: 20px 20px;
}

.our-products-feature>h3,
.our-products-feature>p {
    color: white;
}

.product>.link-horizontal {
    display: inline-flex;
}

.bg-l {
    background: transparent linear-gradient(270deg, #01B0B8 0%, #0594A1 28%, #0D5A71 57%, #133351 100%) 0% 0% no-repeat padding-box;
}

.bg-r {
    background: transparent linear-gradient(90deg, #01B0B8 0%, #0594A1 28%, #0D5A71 57%, #133351 100%) 0% 0% no-repeat padding-box;
}

.custom-app-section {
    background: transparent linear-gradient(180deg, #FFFFFF 0%, #01B0B8 100%) 0% 0% no-repeat padding-box;
    padding-bottom: 0px;
}

.custom-app-section h1 {
    color: #10435E;
    letter-spacing: 0px;
}

.custom-app-section p {
    color: #10435E;
}

.custom-app-divider {
    position: relative;
    top: 6px;
}

@media screen and (max-width: 360px) {
    input[type=checkbox] {
        -ms-transform: scale(1);
        /* IE */
        -moz-transform: scale(1);
        /* FF */
        -webkit-transform: scale(1);
        /* Safari and Chrome */
        -o-transform: scale(1);
        /* Opera */
        padding: 0px;
    }
}

@media screen and (max-width: 500px) {
    .subscribe .form-group input {
        font-size: 16px !important;
    }
    input[type=checkbox] {
        -ms-transform: scale(1);
        /* IE */
        -moz-transform: scale(1);
        /* FF */
        -webkit-transform: scale(1);
        /* Safari and Chrome */
        -o-transform: scale(1);
        /* Opera */
        padding: 0px;
    }
}

@media screen and (max-width: 575px) {
    .mean-container a.meanmenu-reveal {
        top: 0px;
        right: 10px;
    }
    .mean-menu-logo {
        display: block;
        position: absolute;
        left: 0;
        top: -82px;
    }
    .mean-menu-logo>.logo-header {
        max-width: 60px;
        margin: 15px !important;
    }
}

@media screen and (max-width: 767px) {
    .app1-header-divider {
        top: -60px;
    }
    .link-horizontal ul li a.icon-btn i {
        font-size: 27px;
        height: 58px;
        width: 58px;
    }
}

@media screen and (max-width: 992px) and (min-width:768px) {
    .app1-header-divider {
        top: -63px;
    }
    .service-feature>p {
        min-height: 145px;
    }
}

@media screen and (max-width: 1024px) and (min-width:993px) {
    .app1-header-divider {
        top: -130px;
    }
}

@media screen and (max-width: 992px) {
    .dropdown-menu {
        position: relative !important;
        transform: unset !important;
        will-change: unset !important;
        -webkit-transition: unset !important;
        transition: unset !important;
        background: unset !important;
        border: unset !important;
    }
    .navbar-nav {
        background: rgba(31, 67, 86, 0.95);
    }
    .dropdown-item>a,
    .nav-item>a {
        color: #fff !important;
    }
    .mean-menu-logo {
        display: block;
        position: absolute;
        left: 0;
        top: -70px;
    }
}

@media screen and (max-width: 1024px) {
    header.nav-abs>.container {
        margin-top: unset;
    }
}




.mobilesection-1{
    
}


@media (max-width: 940px) {
    .mobilesection-1{
        display: flex;
        flex-direction: column-reverse;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    
}

@media (max-width: 940px) {
    .mobilesection-2{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
}


@media (max-width: 940px) {
    .report-section{
        margin-top: 20px;
        margin-bottom: 40px;
    }
    
}

@media (max-width: 940px) {
    .report-image{
        margin-top: 40px;
    }
    
}

@media (max-width: 940px) {
    .report-image1{
        margin-top: 20px;
      
    }
    
}


.contentarcn{}


@media (max-width: 940px) {
    .contentarcn{
        margin-bottom: 10%;
    }
    
}

/* @media (max-width: 940px) {
    .contentarcn{
        margin-top: 20px;
        margin-bottom: 40px;
    }
    
} */



.report-section {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
}

.report-content {
    width: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    height: auto;
}


@media (max-width: 940px) {
    .report-content {
        width: 90%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        height: auto;
    }
    
    
}

.report-image {
    width: 400px;
    height: 300px;
    
    background-image: url(../images/automation.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.report-image1 {
    width: 400px;
    height: 300px;
    
    background-image: url(../images/communication.webp);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}


.report-text {
    width: 600px;
    height: auto;
    margin-left: 30px;
    text-align: left;
}

@media (max-width: 940px) {
    .report-text {
        width: 600px;
        height: auto;
        margin-left: 0px;
        text-align: left;
    }
    
}

.report-text h3 {
    margin-bottom: 30px;
    
}

.report-text p {
    font-size: 13px;
}



/* .dropdown-hover {
    display: none;
}

.nav-item:hover .dropdown-hover {
    display: block;
    position: absolute;
    z-index: 1000; /* adjust as necessary */
