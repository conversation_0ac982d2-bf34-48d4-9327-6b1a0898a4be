<!DOCTYPE html>
<html lang="en">

<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="Gowell Solutions" name="description">
    <meta content="Gowell Solutions" name="keywords">
    <meta content="Gowell Solutions" name="author">

    <title>Gowell Solutions | Privacy Policy</title>

    <!-- Fav icon -->
    <link href="assets/images/logo/favicon.webp" rel="shortcut icon">
    <!-- Font Family-->
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
    <!--bootstrap css-->
    <link href="assets/css/bootstrap.css" rel="stylesheet" type="text/css">
    <!-- color css -->
    <link href="assets/css/inner-page.css" rel="stylesheet" type="text/css">
    <!--owl carousel css-->

    <!-- MeanMenu CSS -->
    <link rel="stylesheet" href="assets/css/meanmenu.min.css">

    <!-- AOS CSS -->
    <link href="assets/css/aos.css" rel="stylesheet">


    <!-- Icons -->
    <link href="assets/css/fontawesome.css" rel="stylesheet" type="text/css">
    <link href="assets/css/themify.css" rel="stylesheet" type="text/css">

    <!-- BoxIcons Min CSS -->
    <link rel="stylesheet" href="assets/css/boxicons.min.css">

    <!--magnific popup css-->
    <link href="assets/css/magnific-popup.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">

    <!--custom style-->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css">
</head>

<body class="agency" data-offset="50" data-spy="scroll" data-target=".navbar">
    <!--loader start-->
    <div class="loader-wrapper">
        <div class="loader">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
    <!--loader end-->
    <!-- <headr data-include="common/header"></headr> -->


    <header class="app1 nav-abs custom-scroll" style="background-color: #1D456B;">
        <div class="container">
            <div class="row">
                <div class="col">
                    <nav class="navbar navbar-expand-lg navbar-dark ">
                        <a class="d-inline-block m-r-auto" href="./">
                            <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                        </a>
    
                        <div class="collapse navbar-collapse mean-menu" id="navbarSupportedContent">
                            <a class="mean-menu-logo" href="./">
                                <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                            </a>
                            <ul class="navbar-nav ml-auto">
                                <li class="nav-item">
                                    <a  class="nav-link" href="" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/darren-sabet/gowell'});return false;">Schedule A Meeting</a>
                                </li>
    
                                <li class="nav-item">
                                    <a  class="nav-link" href="./">About Us</a>
                                </li>
    
                                <li class="nav-item" id="Solutions">
                                    <a  class="nav-link" href="#" onclick="toggleDropdown()">
                                        Solutions <i class='bx bx-chevron-down'></i>
                                    </a>
                                </li>
    
                                <li id="contact" class="nav-item">
                                    <a  class="nav-link" href="./contact.html">Contact Us</a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <div id="dropdownContent" style="display: none; position: absolute; top: 60px; right: 0;  width: 350px; z-index: 999; transition: ease-in-out;">
        <!-- Dropdown content provided in the question -->
        <div style="padding: 10px; margin-top: 10px;">
            <!-- Insert your dropdown content here -->
            <div style="width: 350px; height: fit-content; background-color: rgb(255, 255, 255); display: flex;  flex-direction: column; color: white;">
                <div style="padding: 10px;">
                  
                        <ul style="display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">
                                <a style="font-size: 20px; color: #1D456B;" href="./bespoke_solutions.html">Bespoke Solutions</a>
                            </p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/agriculture.webp" style="width: 40px; height: 40px;"/>
                                <p> <a href="./arcn.html"> Agricultural Research MIS Solution</a>
                                </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/instacare_logo.webp" style="width: 40px; height: 40px; "/>
                                <p><a href="./instacare.html">instaCare</a>
                                    </p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/enterprise_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./Emsplus.html">Enterprise Management System Plus (EMS Plus)</a></p>
                            </div>
                        </ul>
                  
               
                        <ul style="margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Commercial Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/edana_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edana.html"> Edana.co</a>
                                   </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/ed-admin_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edadmin.html">Ed-admin</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/headstart_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./headstart_academy.html">Headstart Academy</a></p>
                            </div>
                        </ul>
                        <ul  style=" margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Social Responsibility Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img src="https://play-lh.googleusercontent.com/__4aC117XQcBDEC7AZX5d_mGY5Hyiw3PCWJYXLgaVR_JM0zAZ6LONFUXNNT8xodJkk0=w480-h960-rw" style="width: 30px; height: 30px; border-radius: 50%;"/>
                                <p><a href="./wordsinspire.html">Words to Inspire App</a></p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/4RmFML5nmtXAE1tS7RouxEoKgpnWLA7AFSAznCdeJkof8FnEKU655iRw-qS2j1Slwbw=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_life.html">Living a Baha’i Life App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/JNIj29ffdxFlCP9U1vHS-MHpO7t7wJn2LBC4-5VW1wJwn4PRUCTLr2n82U10meGBI-Q=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_beats.html">Baha’i Beats App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 3px;">
                                <img src="./assets/B_lex.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./B_Lex.html">B.Lex App</a></p>
                            </div>
                        </ul>
                    
                </div>
                
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown() {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
   
       if (dropdownContent.style.display === "none") {
           dropdownContent.style.display = "block";
           document.addEventListener("click", closeDropdownOutside);
       } else {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
   
   function closeDropdownOutside(event) {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
       var isClickInsideContent = dropdownContent.contains(event.target);
       var isClickInsideButton = dropdownButton.contains(event.target);
   
       if (!isClickInsideContent && !isClickInsideButton) {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
       </script>
    <span data-menuid="" class="d-none"></span>

    <!--megamenu section Start-->
    <div class="loding-header pt-5 pb-5">


    </div>
    <!--megamenu section end-->


    <!-- about section Start-->



    <section class="gym format p-50">
        <div class="container">
            <div class="row">
                <div class="col-md-12" style="text-align:center;">
                    <div class="center-text">
                        <div class="left-text">
                            <div class="format-head-text">
                                <h3 class="about-font-header gradient-text p-light">Privacy Policy</h3>
                            </div>
                            <div class="format-sub-text mb-0">
                                <p class="p-light about-para">
                                    Please read these Terms carefully before using this app operated by Gowell Solutions (a sister company to Ed-admin Pty Ltd, based in Australia). <br> We at Gowell Solutions ("Gowell Solutions", "we", "us", "our”) know
                                    that you care about how your personal information is used and shared, and we take your privacy seriously. By accessing or using the Services of the Gowell Solutions mobile application(s) available via the Apple App
                                    Store and the Android Marketplace (collectively, the “Mobile Application”), you acknowledge that you accept the practices and policies outlined in this Privacy Policy.


                                </p>

                                <p class="p-light about-para">

                                    This Privacy Policy explains how Gowell Solutions collects and uses information from its Services users, including our treatment of personally identifiable information. This policy shall not apply to websites or practices of companies that Gowell Solutions
                                    does not own or control, or to individuals that Gowell Solutions does not employ or manage: <br>
                                </p>
                                <ul class="p-light about-para">
                                    <li>We receive and store any information you voluntarily submit to us, whether via computers, mobile phones, other wireless devices, or that you provide to us in any other way. This information may include, without limitation,
                                        Personal Information such as your name, user name, email address, phone number, profile picture, location, and any other information necessary for us to provide our Services.</li><br>
                                    <li>We receive and store certain types of usage information whenever you interact with the Services; this information is not Personal Information. For example, when you download our Mobile Application, we automatically
                                        collect information on the type of device, name, and version of the operating system, name and version of the application, the numerical mobile device user ID, as well as actions performed by the user in accessing
                                        the Mobile Application. However, we do not ask you for, access or track any geographic-location-based information from your mobile device (i.e., any information regarding where your mobile device is located on the
                                        globe at a particular time) at any time while downloading or using our Mobile Applications.</li><br>
                                    <li>When you use the Services, you may set up your personal profile, form relationships, send messages, perform searches and queries, and transmit information through various channels, depending on the category of user
                                        ("User Category") you are registered as, and as permitted by the functionality of the Services. The information we gather from users enables us to personalise and improve our services and allows users to set up
                                        a user account and profile through the Services.</li><br>
                                    <li>The Personal Information you provide is used for such purposes as responding to your requests for certain information and services, customising your experience, and communicating with you about the Services.</li><br>
                                    <li>Mobile usage information is used to determine the device capabilities and usage trends which allows us to better understand which devices we should support and what features are most frequently used.</li><br>
                                    <li>We will send notifications to your mobile device in order to make you aware of certain activities that may occur within your account, such as when someone has entered a new post or when you have new notifications. You
                                        may opt-out of receiving these types of communications by turning them off at the device level.</li>
                                </ul>

                                <h4 class=" p-light">Will Gowell Solutions share any of the personal information it receives? </h4>
                                <p class="p-light about-para">
                                    Personal Information about our users is an integral part of our business.
                                    <b>We neither rent nor sell your Personal Information; we may share your Personal Information in personally

							identifiable form only as described below.</b><br>
                                </p>
                                <ul class="p-light about-para">
                                    <li>In certain situations, businesses or third party websites we're affiliated with may offer or sell items or provide services to you through the Services (either alone or jointly with us), including Publisher Software.
                                        We may, for example, work jointly with other businesses to offer or sell products or provide services, or we may work with third-party websites to enhance your online experience. These transactions or services may
                                        or may not be commercial in nature. You can recognise when such a business is associated with such a transaction or service. We will share your Personal Information with that business only to the extent that it
                                        is related to such transaction or service.</li><br>
                                    <li>We may employ other companies and people to perform tasks on our behalf and need to share your information with them to provide products or services to you. Examples include sending email, analysing data, processing
                                        payments, and providing user services. Unless we tell you differently, Gowell Solutions's agents do not have any right to use Personal Information we share with them beyond what is necessary to assist us.</li><br>
                                    <li>We may release Personal Information when we believe in good faith that release is necessary to comply with the law (such as to comply with a subpoena); enforce or apply our Terms of Use and other agreements; or protect
                                        the rights, property, or safety of Gowell Solutions, our employees, our users, or others. We will try to give you notice if we release information for these reasons, but please understand that we reserve the right
                                        not to, as it may not be practical, legal, or safe to do so.</li><br>
                                </ul>
                                <h4 class="p-light">Information Security</h4>
                                <ul class="p-light about-para">
                                    <li>Your Gowell Solutions account Personal Information is protected by a password for your privacy and security. You may help protect against unauthorised access to your account and Personal Information by selecting and
                                        protecting your password appropriately and limiting access to your computer and browser by signing off after you have finished accessing your account. </li><br>
                                    <li>Gowell Solutions has implemented reasonable security measures in order to protect your information from loss, misuse, unauthorised access, disclosure, alteration or destruction. While we cannot guarantee that loss,
                                        misuse or alteration to information will not occur, we make good faith efforts to prevent such occurrence. We do not warrant or represent that your account or any of your information will be protected against loss,
                                        misuse or alteration by third parties.</li><br>
                                    <li>The Services contain links to other sites. We shall not be responsible for the privacy policies and/or practices on other sites (but you must read all third parties’ privacy policies and ensure you understand them).
                                        This Privacy Policy only governs information collected by Gowell Solutions on the Services. </li><br>
                                    <li>Please understand that we may be required to disclose an individual’s personal information in response to a lawful request by public authorities, including to meet national security or law enforcement requirements.
                                        It is not our intent to pass on any such personal information to public authorities, but in the identified circumstances, we may be required to do so. </li><br>
                                    <li>These policies may be revised at any time without notice. </li> <br> </ul>
                               
                            </div>

                        </div>
                    </div>
                </div>
            </div>
    </section>
    <!-- about section end-->


    <!-- footer start -->
    <!-- <footer class="app2 bg footer2 p-0 inner">
        <section>
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 text-center">
                        <div class="logo-sec">
                            <div class="footer-contant">
                                <img alt="" class="logo-footer" src="../assets/images/logo/gowell-solutions.webp">
                                <div class="footer-para">
                                    <p class="text-white p-light">Instacare aims at helping healthcare professionals monitor or diagnose patients easily and quickly using mobile devices.</p>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
    </footer> -->
    <!-- footer end -->


    <!-- copyright start -->
    <div class="agency copyright inner-page">
        <div class="container">
            <div class="row">
                <div class="col-sm-6">
                    <div class="link-horizontal">
                        <ul>
                            <li><a class="copyright-text p-light" href="./contact.html">Contact</a></li>
                            <li><a class="copyright-text p-light" href="./privacypolicy.html">Privacy Policy</a></li>
                            <li><a class="copyright-text p-light" href="./termsofuse.html">Terms of Us</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div>
                        <h6 id="copyright" class="copyright-text text-white text-right">Copyright © <span id="currentYear"></span> Gowell Solutions</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById("currentYear").innerText = new Date().getFullYear();
    </script>
    
    <!-- copyright end -->

    <!-- Tap on Top-->
    <div class="tap-top">
        <div><i class="fa fa-angle-double-up"></i></div>
    </div>
    <!-- Tap on Ends-->

    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>
    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>
    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>
    <!--magnific popup js-->
    <script src="assets/js/magnific-popup.js"></script>
    <!--  costamizer option -->
    <!--owl js-->
    <script src="assets/js/owl.carousel.min.js"></script>
    <!-- script js-->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/testimonial.js"></script>
    <script src="assets/js/video-popup.js"></script>
    <script src="assets/js/aos-init.js"></script>
    <!-- MeanMenu JS -->
    <script src="assets/js/jquery.meanmenu.min.js"></script>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            const bespoke = document.getElementById('bespoke');
            bespoke.addEventListener('click', function(event) {
                console.log("Clicked on 'bespoke'");
                event.preventDefault();
                window.location.href = '../bespoke_solutions.html';
            });
        });
    </script>

</body>

</html>