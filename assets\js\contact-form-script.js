/*==============================================================*/
// Gowell Solutions Contact Form JS
/*==============================================================*/
(function ($) {
    "use strict"; // Start of use strict
    $("#contactForm").validator().on("submit", function (event) {
        if (event.isDefaultPrevented()) {
            // handle the invalid form...
            formError();
            submitMSG(false, "Please fill in all required fields properly.");
        } else {
            // everything looks good!
            event.preventDefault();
            submitForm($(this));
        }
    });

    function submitForm(thisForm){
        // Show loader
        showLoader(true);

        // Prepare form data
        var formData = {
            name: thisForm.find('input[name="name"]').val(),
            email: thisForm.find('input[name="email"]').val(),
            country: thisForm.find('input[name="country"]').val(),
            organisation: thisForm.find('input[name="organisation"]').val(),
            message: thisForm.find('textarea[name="message"]').val()
        };

        $.ajax({
            type: "POST",
            url: "http://localhost:3001/api/contact",
            contentType: "application/json",
            data: JSON.stringify(formData),
            timeout: 10000, // 10 second timeout
            success: function(response){
                showLoader(false);
                if (response.success === true){
                    formSuccess(response.message);
                } else {
                    formError();
                    submitMSG(false, response.message || "Failed to send message. Please try again.");
                }
            },
            error: function(xhr, status, error){
                showLoader(false);
                formError();
                if (status === 'timeout') {
                    submitMSG(false, "Request timed out. Please check your connection and try again.");
                } else if (xhr.status === 0) {
                    submitMSG(false, "Unable to connect to server. Please check if the backend is running.");
                } else {
                    submitMSG(false, "An error occurred while sending your message. Please try again later.");
                }
            }
        });
    }

    function showLoader(show) {
        var submitBtn = $("#submitBtn");
        var btnText = $("#btnText");
        var btnLoader = $("#btnLoader");

        if (show) {
            submitBtn.prop('disabled', true);
            btnText.hide();
            btnLoader.show();
        } else {
            submitBtn.prop('disabled', false);
            btnText.show();
            btnLoader.hide();
        }
    }

    function formSuccess(message){
        $("#contactForm")[0].reset();
        submitMSG(true, message || "Thank you! Your message has been sent successfully. We will get back to you soon!");

        // Scroll to message
        $('html, body').animate({
            scrollTop: $("#msgSubmit").offset().top - 100
        }, 500);

        // Auto-hide success message after 5 seconds
        setTimeout(function() {
            $("#msgSubmit").fadeOut();
        }, 5000);
    }

    function formError(){
        $("#contactForm").addClass('shake');
        setTimeout(function() {
            $("#contactForm").removeClass('shake');
        }, 500);
    }

    function submitMSG(valid, msg){
        var msgElement = $("#msgSubmit");
        msgElement.removeClass();

        if(valid){
            msgElement.addClass("message-success").html('<i class="bx bx-check-circle"></i> ' + msg);
        } else {
            msgElement.addClass("message-error").html('<i class="bx bx-error-circle"></i> ' + msg);
        }

        msgElement.show();
    }
}(jQuery)); // End of use strict