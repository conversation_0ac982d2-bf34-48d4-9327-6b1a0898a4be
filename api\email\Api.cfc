<cfcomponent>
<cfinclude template="../../dsn.cfm">

<cffunction name="GetMailServerDet" returntype="struct" output="false">

	<cfset var sMailServer = StructNew() />

	<cfquery name="getMailServer" datasource="#dsn#" cachedwithin="#CreateTimeSpan(0, 0, 1, 0)#">
		SELECT * FROM "vMailServer"
	</cfquery>

	<cfif Trim(getMailServer.Value[1]) NEQ "">

		<cfset sMailServer.Server = Trim(getMailServer.Value[1])>

		<cfif Trim(getMailServer.Value[2]) NEQ "">
			<cfset sMailServer.Port = Trim(getMailServer.Value[2])>
		</cfif>

		<cfif Trim(getMailServer.Value[3]) NEQ "">
			<cfset sMailServer.UserName = Trim(getMailServer.Value[3])>
		</cfif>

		<cfif Trim(getMailServer.Value[4]) NEQ "">
			<cfset sMailServer.Password = Trim(getMailServer.Value[4])>
		</cfif>

	</cfif>
	
	<cfreturn sMailServer />

</cffunction>
<cffunction name="subscription" displayname="Set subscription" access="remote" returntype="Any" returnformat="JSON">
<cfargument name="EMAIL" required="true" type="string" />	
		<cfset response = structNew()>
		<cfset device ="Unknown">
		<cfif findNoCase('Android', cgi.http_user_agent,1)>
				<cfset device ="Android">
		<cfelseif findNoCase('iPhone', cgi.http_user_agent,1)>
				<cfset device ="Iphone">
		<cfelseif findNoCase('Windows', cgi.http_user_agent,1)>
				<cfset device ="Windows">
		<cfelseif findNoCase('Linux', cgi.http_user_agent,1)>
				<cfset device ="Linux">
		</cfif>
		<cfquery name="Insert_Subscriber" datasource="#dsn#">
			INSERT INTO "SUBSCRIPTION" ("EMAIL", "CREATED_AT", "IP_ADDRESS", "DEVICE", "STATUS") 
			VALUES ('#arguments.EMAIL#', #Now()#,'#CGI.REMOTE_ADDR#', '#device#', 1)
		</cfquery>
		<cftry>
			<cfset sServer = GetMailServerDet()>
			<cfmail to=#arguments.EMAIL# type="html"
			from = "<EMAIL>" subject = "Ed-admin Subscription" attributeCollection="#sServer#"> 
			<cfinclude template="./subscribe_template.cfm">
			</cfmail>

			<cfcatch type="any">
					<cfset response.success = false>
					<cfset response.message = 'Sending Email Failed'>
					<cfreturn response>
			</cfcatch>
		</cftry>
		<cfset response.success = true>
		<cfset response.message = 'Subscription Done'>
		<cfreturn response>

</cffunction>
<cffunction name="Contact" displayname="Contact us" access="remote" returntype="Any" returnformat="JSON">
<cfargument name="name" required="true" type="string" />
<cfargument name="email" required="true" type="string" />
<cfargument name="country" required="true" type="string" />
<cfargument name="organisation" required="true" type="string" />
<cfargument name="message" required="true" type="string" />
		<cfset response = structNew()>
		<cftry>
			<cfquery name="Insert_Subscriber" datasource="#dsn#">
				INSERT INTO "Contact" ("NAME", "EMAIL", "ORGANISATION", "COUNTRY", "MESSAGE" ,"CREATED_AT") 
				VALUES ('#arguments.name#', '#arguments.email#', '#arguments.organisation#', '#arguments.country#', '#arguments.message#', #Now()#)
			</cfquery>
			<cfset sServer = GetMailServerDet()>
			<cfmail to="<EMAIL>" type="html"
			from = #arguments.EMAIL# subject = "Ed-admin Contact" attributeCollection="#sServer#"> 
			<html>
				<body>
					<h1>Ed-admin Contact</h1>
					<p>Name: #arguments.name#</p>
					<p>EMAIL: #arguments.email#</p>
					<p>PHONE: #arguments.organisation#</p>
					<p>SUBJECT: #arguments.country#</p>
					<p>MESSAGE: #arguments.message#</p>
				</body>
			</html>
			</cfmail>
			<cfcatch type="any">
					<cfset response.success = false>
					<cfset response.message = 'submission failed'>
					<cfreturn response>
			</cfcatch>
		</cftry>
		<cfset response.success = true>
		<cfset response.message = 'We will contact with you as soon as possible'>
		<cfreturn response>

</cffunction>
</cfcomponent>