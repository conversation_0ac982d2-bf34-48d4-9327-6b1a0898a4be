/*! Blue Monday Skin for jPlayer 2.9.2 ~ (c) 2009-2014 Happyworm Ltd ~ MIT License */

.jp-audio :focus,
.jp-audio-stream :focus,
.jp-video :focus {
    outline: 0
}

.jp-audio button::-moz-focus-inner,
.jp-audio-stream button::-moz-focus-inner,
.jp-video button::-moz-focus-inner {
    border: 0
}

.button-container button{
    margin: 0 10px;
}

.jp-audio {
    width: 100%
}


.jp-interface {
    position: relative;
    width: 100%
}

.jp-video .jp-interface {
    border-top: 1px solid #009be3
}

.jp-controls-holder {
    clear: both;
    width: 440px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    top: -8px
}

.jp-interface .jp-controls {
    margin: 0;
    padding: 0;
    overflow: hidden
}

.jp-audio .jp-controls {
    width: 100%;
    display: flex;
    justify-content: center;
}

.jp-audio-stream .jp-controls {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 142px
}

.jp-video .jp-type-single .jp-controls {
    width: 78px;
    margin-left: 200px
}

.jp-video .jp-type-playlist .jp-controls {
    width: 134px;
    margin-left: 172px
}

.jp-video .jp-controls {
    float: left
}

.jp-controls button {
    border: none;
    cursor: pointer;
    color: white;
    background-color: transparent;
    padding: 0;
}

.jp-play, .jp-play:focus, .jp-state-playing .jp-play,.jp-state-playing .jp-play:focus,.jp-previous,.jp-previous:focus,.jp-next,.jp-next:focus,.jp-stop,.jp-stop:focus {
    font: normal normal normal 14px/1 FontAwesome;
}
.jp-play:before {
    content: "\f04b";
}

.jp-state-playing .jp-play:before {
    content: "\f04c";
}




.jp-previous:before {
    content: "\f048";
}

.jp-next:before {
    content: "\f051";
}

.jp-progress {
    overflow: hidden;
    background-color: rgba(128, 128, 128, 0.23);
}

.jp-audio .jp-progress {
    top: 32px;
    height: 4px;
}

.jp-audio .jp-type-single .jp-progress {
    left: 110px;
    width: 186px
}

.jp-audio .jp-type-playlist .jp-progress {
    left: 166px;
    width: 100%
}

.jp-video .jp-progress {
    top: 0;
    left: 0;
    width: 100%;
    height: 10px
}

.jp-seek-bar {
    width: 0;
    height: 100%;
    cursor: pointer
}

.jp-play-bar {
    background:#1b2873;
    width: 0;
    height: 100%
}

.jp-seeking-bg {
    background: url(../image/jplayer.blue.monday.seeking.gif)
}

.jp-state-no-volume .jp-volume-controls {
    display: none
}

.jp-volume-controls {
    position: absolute;
    top: 50px;
    right: 0;
    width: 200px
}

.jp-audio-stream .jp-volume-controls {
    left: 70px
}

.jp-video .jp-volume-controls {
    top: 12px;
    left: 50px
}

.jp-volume-controls button {
    display: block;
    position: absolute;
    overflow: hidden;
    text-indent: -9999px;
    border: none;
    cursor: pointer
}

.jp-mute,
.jp-volume-max {
    width: 25px;
    height: 25px
}

.jp-volume-max {
    left: 115px
}

.jp-mute {
    background: url(../images/music/video/mute.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-mute:focus {
    background: url(../images/music/video/mute.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-state-muted .jp-mute {
    background: url(../images/music/video/volume.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-state-muted .jp-mute:focus {
    background: url(../images/music/video/volume.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-volume-max {
    background: url(../images/music/video/volume.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-volume-max:focus {
    background: url(../images/music/video/volume.webp) no-repeat;
    filter: brightness(0) invert(1);
    background-size: 25px;
}

.jp-volume-bar {
    position: absolute;
    overflow: hidden;
    background-color: rgba(182, 41, 41, 0.4);
    top: 10px;
    left: 34px;
    width: 70px;
    height: 5px;
    cursor: pointer;
}

.jp-volume-bar-value {
    background-color: rgba(182, 41, 41, 0.4);
    width: 0;
    height: 5px
}

.jp-audio .jp-time-holder {
    padding-top:15px
}

.jp-audio .jp-type-single .jp-time-holder {
    left: 110px;
    width: 186px
}

.jp-audio .jp-type-playlist .jp-time-holder {
    width: 100%
}

.jp-current-time,
.jp-duration {
    width: 60px;
    color: #808080;
    font-size: 12px;
}

.jp-current-time {
    float: left;
    display: inline;
    cursor: default;
}

.jp-duration {
    float: right;
    display: inline;
    text-align: right;
    cursor: pointer
}

.jp-video .jp-current-time {
    margin-left: 20px
}

.jp-video .jp-duration {
    margin-right: 20px
}

.jp-details {
    font-weight: 700;
    text-align: center;
    cursor: default
}

.jp-type-playlist .jp-details,
.jp-type-single .jp-details {
    border-top: none
}

.jp-details .jp-title {
    margin: 0;
    padding: 5px 20px;
    font-size: .72em;
    font-weight: 700
}

.jp-playlist ul {
    list-style-type: none;
    margin: 0;
    padding: 0 20px;
    font-size: .72em
}

.jp-playlist li {
    padding: 10px 0 10px 20px;
    border-bottom: 1px solid #444444;
}

.jp-playlist li div {
    display: inline
}

div.jp-type-playlist div.jp-playlist li:last-child {
    padding: 5px 0 5px 20px;
    border-bottom: none
}

div.jp-type-playlist div.jp-playlist li.jp-playlist-current {
    list-style-type: square;
    list-style-position: inside;
    padding-left: 7px
}

div.jp-type-playlist div.jp-playlist a {
    color: #c9c9c9;
    text-decoration: none
}

div.jp-type-playlist div.jp-playlist a.jp-playlist-current,
div.jp-type-playlist div.jp-playlist a:hover {
    color: #233e80
}

div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove {
    float: right;
    display: inline;
    text-align: right;
    margin-right: 10px;
    font-weight: 700;
    color: #666
}

div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove:hover {
    color: #0d88c1
}

div.jp-type-playlist div.jp-playlist span.jp-free-media {
    float: right;
    display: inline;
    text-align: right;
    margin-right: 10px
}

div.jp-type-playlist div.jp-playlist span.jp-free-media a {
    color: #666
}

div.jp-type-playlist div.jp-playlist span.jp-free-media a:hover {
    color: #0d88c1
}

span.jp-artist {
    font-size: .8em;
    color: #666
}

.jp-video-play {
    width: 100%;
    overflow: hidden;
    cursor: pointer;
    background-color: transparent
}

.jp-video-270p .jp-video-play {
    margin-top: -270px;
    height: 270px
}

.jp-video-360p .jp-video-play {
    margin-top: -360px;
    height: 360px
}

.jp-video-full .jp-video-play {
    height: 100%
}

.jp-video-play-icon {
    position: relative;
    display: block;
    width: 112px;
    height: 100px;
    margin-left: -56px;
    margin-top: -50px;
    left: 50%;
    top: 50%;
    background: url(../image/jplayer.blue.monday.video.play.webp) no-repeat;
    text-indent: -9999px;
    border: none;
    cursor: pointer
}

.jp-video-play-icon:focus {
    background: url(../image/jplayer.blue.monday.video.play.webp) 0 -100px no-repeat
}

.jp-jplayer,
.jp-jplayer audio {
    width: 0;
    height: 0
}

.jp-toggles {
    padding: 0;
    margin: 0 auto;
    overflow: hidden
}

.jp-audio .jp-type-single .jp-toggles {
    width: 25px
}

.jp-audio .jp-type-playlist .jp-toggles {
    width: 55px;
    margin: 0;
    position: absolute;
    left: 325px;
    top: 50px
}

.jp-video .jp-toggles {
    position: absolute;
    right: 16px;
    margin: 10px 0 0;
    width: 100px
}

.jp-toggles button {
    display: block;
    float: left;
    width: 25px;
    height: 18px;
    text-indent: -9999px;
    line-height: 100%;
    border: none;
    cursor: pointer
}

.jp-full-screen {
    background: url(../image/jplayer.blue.monday.webp) 0 -310px no-repeat;
    margin-left: 20px
}

.jp-full-screen:focus {
    background: url(../image/jplayer.blue.monday.webp) -30px -310px no-repeat
}

.jp-state-full-screen .jp-full-screen {
    background: url(../image/jplayer.blue.monday.webp) -60px -310px no-repeat
}

.jp-state-full-screen .jp-full-screen:focus {
    background: url(../image/jplayer.blue.monday.webp) -90px -310px no-repeat
}

.jp-repeat {
    background: url(../image/jplayer.blue.monday.webp) 0 -290px no-repeat
}

.jp-repeat:focus {
    background: url(../image/jplayer.blue.monday.webp) -30px -290px no-repeat
}

.jp-state-looped .jp-repeat {
    background: url(../image/jplayer.blue.monday.webp) -60px -290px no-repeat
}

.jp-state-looped .jp-repeat:focus {
    background: url(../image/jplayer.blue.monday.webp) -90px -290px no-repeat
}

.jp-shuffle {
    background: url(../image/jplayer.blue.monday.webp) 0 -270px no-repeat;
    margin-left: 5px
}

.jp-shuffle:focus {
    background: url(../image/jplayer.blue.monday.webp) -30px -270px no-repeat
}

.jp-state-shuffled .jp-shuffle {
    background: url(../image/jplayer.blue.monday.webp) -60px -270px no-repeat
}

.jp-state-shuffled .jp-shuffle:focus {
    background: url(../image/jplayer.blue.monday.webp) -90px -270px no-repeat
}

.jp-no-solution {
    padding: 5px;
    font-size: .8em;
    background-color: #eee;
    border: 2px solid #009be3;
    color: #000;
    display: none
}

.jp-no-solution a {
    color: #000
}

.jp-no-solution span {
    font-size: 1em;
    display: block;
    text-align: center;
    font-weight: 700
}