<!DOCTYPE html>
<html lang="en">

<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <!-- <meta content="Gowell Solutions" name="description"> -->
    <meta content="Gowell Solutions" name="keywords">
    <meta content="Gowell Solutions" name="author">
    <meta name="description" content="Gowell Solutions: Solutions towards Betterment of Humanity...https://www.gowell.solutions
        We're passionate about using software to improve the wellbeing of humanity. Our team believes that technology can be a powerful tool for creating positive change, and we're dedicated to developing innovative software solutions…">

    <title>Gowell Solutions: Bahai beats</title>

    <!-- Fav icon -->
    <link href="assets/images/logo/favicon.webp" rel="shortcut icon">

    <!-- Font Family-->
    <link href="https://fonts.googleapis.com/css?family=Capriola&amp;subset=latin-ext" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Varela%20Round:100,200,300,400,500,600,700,800,900" rel="stylesheet">

    <!--bootstrap css-->
    <link href="assets/css/bootstrap.css" rel="stylesheet" type="text/css">

    <!-- color css -->
    <link href="assets/css/color-1.css" rel="stylesheet" type="text/css">

    <!--keyframe css-->
    <link href="assets/css/keyframes.css" rel="stylesheet">

    <!--owl carousel css-->
    <link href="assets/css/owl.carousel.min.css" rel="stylesheet">
    <link href="assets/css/owl.theme.default.min.css" rel="stylesheet">

    <!-- Swiper CSS -->
    <link href="assets/css/swiper.min.css" rel="stylesheet">

    <!-- BoxIcons Min CSS -->
    <link rel="stylesheet" href="assets/css/boxicons.min.css">

    <!-- MeanMenu CSS -->
    <link rel="stylesheet" href="assets/css/meanmenu.min.css">

    <!-- AOS CSS -->
    <link href="assets/css/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- Icons -->
    <link href="assets/css/fontawesome.css" rel="stylesheet" type="text/css">
    <link href="assets/css/themify.css" rel="stylesheet" type="text/css">

    <!--magnific popup css-->
    <link href="assets/css/magnific-popup.css" rel="stylesheet">

    <!--custom style-->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css">
    <style>
        .widgetLabel2{
            
            height: 42px;
            bottom: 61px;
            z-index: 1;
            white-space: nowrap;
            font-size: 17px;
            line-height: 17px;
            border-radius: 16px;
            padding: 10px 15px;
            background: rgb(255, 255, 255);
            font-family: "Mulish", sans-serif;
            letter-spacing: -0.24px;
            color: rgb(6, 19, 43);
            white-space: nowrap;
            font-size: 17px;
            line-height: 17px;
        }
        .emoji2 {
            width: 20px;
            margin: 0px 2px -5px;
            user-select: none;
        }

        @media (max-width: 1300px) {
        .topP {
            max-height: 300px;
            overflow: auto; }
         }
    </style>

<style>
    body, html {
        margin: 0;
        padding: 0;
    }
    .parallax {
        width: 100%;
        height: 100vh;
        background-color: rgb(50, 42, 42);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.52) 0%, rgba(0, 0, 0, 0.52) 100%), url(./assets/images/audiobook.webp);
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        overflow: hidden;
        position: relative;
    }
    .content-1 {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 30px;
        width: 70%;
        color: white;
        position: relative;
        z-index: 1;
    }
    .background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url(./assets/images/school.webp);
        background-size: cover;
        background-position: center;
        filter: blur(5px);
        z-index: -1;
    }
</style>


    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-6W0RY642JT"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-6W0RY642JT');
    </script>

</head>

<body data-offset="50" data-spy="scroll" data-target=".navbar" class="">

    <!--loader start-->
    <div class="loader-wrapper">
        <div class="loader">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>

    
        <!-- <headr data-include="common/header"></headr> -->
    <span data-menuid="home" class="d-none"></span>

    <header class="app1 nav-abs custom-scroll">
        <div class="container">
            <div class="row">
                <div class="col">
                    <nav class="navbar navbar-expand-lg navbar-dark ">
                        <a class="d-inline-block m-r-auto" href="./">
                            <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                        </a>
    
                        <div class="collapse navbar-collapse mean-menu" id="navbarSupportedContent">
                            <a class="mean-menu-logo" href="./">
                                <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                            </a>
                            <ul class="navbar-nav ml-auto">
                                <li class="nav-item">
                                    <a class="nav-link" href="" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/darren-sabet/gowell'});return false;">Schedule A Meeting</a>
                                </li>
    
                                <li class="nav-item">
                                    <a class="nav-link" href="./">About Us</a>
                                </li>
    
                                <li class="nav-item" id="Solutions">
                                    <a class="nav-link" href="#" onclick="toggleDropdown()">
                                        Solutions <i class='bx bx-chevron-down'></i>
                                    </a>
                                </li>
    
                                <li id="contact" class="nav-item">
                                    <a class="nav-link" href="./contact.html">Contact Us</a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>


    <div id="dropdownContent" style="display: none; position: absolute; top: 60px; right: 0;  width: 350px; z-index: 999; transition: ease-in-out;">
        <!-- Dropdown content provided in the question -->
        <div style="padding: 10px; margin-top: 10px;">
            <!-- Insert your dropdown content here -->
            <div style="width: 350px; height: fit-content; background-color: rgb(255, 255, 255); display: flex;  flex-direction: column; color: white;">
                <div style="padding: 10px;">
                  
                        <ul style="display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">
                                <a style="font-size: 20px; color: #1D456B;" href="./bespoke_solutions.html">Bespoke Solutions</a>
                            </p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/agriculture.webp" style="width: 40px; height: 40px;"/>
                                <p> <a href="./arcn.html"> Agricultural Research MIS Solution</a>
                                </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/instacare_logo.webp" style="width: 40px; height: 40px; "/>
                                <p><a href="./instacare.html">instaCare</a>
                                    </p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/enterprise_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./Emsplus.html">Enterprise Management System Plus (EMS Plus)</a></p>
                            </div>
                        </ul>
                  
               
                        <ul style="margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Commercial Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/edana_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edana.html"> Edana.co</a>
                                   </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/ed-admin_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edadmin.html">Ed-admin</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/headstart_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./headstart_academy.html">Headstart Academy</a></p>
                            </div>
                        </ul>
                        <ul  style=" margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Social Responsibility Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img src="https://play-lh.googleusercontent.com/__4aC117XQcBDEC7AZX5d_mGY5Hyiw3PCWJYXLgaVR_JM0zAZ6LONFUXNNT8xodJkk0=w480-h960-rw" style="width: 30px; height: 30px; border-radius: 50%;"/>
                                <p><a href="./wordsinspire.html">Words to Inspire App</a></p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/4RmFML5nmtXAE1tS7RouxEoKgpnWLA7AFSAznCdeJkof8FnEKU655iRw-qS2j1Slwbw=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_life.html">Living a Baha’i Life App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/JNIj29ffdxFlCP9U1vHS-MHpO7t7wJn2LBC4-5VW1wJwn4PRUCTLr2n82U10meGBI-Q=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_beats.html">Baha’i Beats App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 3px;">
                                <img src="./assets/B_lex.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./B_Lex.html">B.Lex App</a></p>
                            </div>
                        </ul>
                    
                </div>
                
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown() {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
   
       if (dropdownContent.style.display === "none") {
           dropdownContent.style.display = "block";
           document.addEventListener("click", closeDropdownOutside);
       } else {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
   
   function closeDropdownOutside(event) {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
       var isClickInsideContent = dropdownContent.contains(event.target);
       var isClickInsideButton = dropdownButton.contains(event.target);
   
       if (!isClickInsideContent && !isClickInsideButton) {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
       </script>
    



    <section class="parallax" id="parallax">
        <div class="section-1-container" style="">
            <h1>Baha’i Beats App</h1>
            <p style="color: white; padding-left: 5%; padding-right: 5%; text-align: center; font-size: 15px;">Baha'i Beatz is an innovative platform dedicated to sharing uplifting content from various faiths to promote unity and diversity across the globe. It features a vast collection of songs, prayers, stories, and talks aimed at inspiring and educating users.</p>
        </div>
        <div class="background" id="background"></div>
    </section>

    <section data-aos="fade-up" data-aos-duration="1000" style="display: flex; justify-content: center;">
        <div class="section-2-text" style="">
            <p style="font-size: 15px;">The platform encourages contributions from its community, ensuring all content adheres to high standards of respect and inclusivity. With a focus on breaking language barriers, Baha'i Beatz offers a unique space for individuals to connect, share, and explore the richness of different cultures and spiritual traditions.
            </p>
        </div>
    </section>


   

 



    



    





    <div class="agency copyright inner-page">
        <div class="container">
            <div class="row">
                <div class="col-sm-6">
                    <div class="link-horizontal">
                        <ul>
                            <li><a class="copyright-text" href="./contact.html">Contact</a></li>
                            <li><a class="copyright-text" href="./privacypolicy.html">Privacy Policy</a></li>
                            <li><a class="copyright-text" href="./termsofuse.html">Terms of Us</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div>
                        <h6 id="copyright" class="copyright-text text-white text-right">Copyright © <span id="currentYear"></span> Gowell Solutions</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById("currentYear").innerText = new Date().getFullYear();
    </script>





    <div class="tap-top">
        <div><i class="fa fa-angle-double-up"></i></div>
    </div>


    <style>
        .section-1-container{
            display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 30px; width: 70%; text-align: center;
        }

        @media (max-width: 940px) {
            .section-1-container{
            display: flex; justify-content: center; align-items: center; flex-direction: column; gap: 30px; width: 90%; text-align: center;
        }
        }

        .section-2-text{
            width: 70%; text-align: center;
        }

        @media (max-width: 940px) {
            .section-2-text{
            width: 90%; text-align: center;
        }
        }


    </style>

    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>

    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>

    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>
    <!--  costamizer option -->
    <script src="assets/js/custamizer-option.js"></script>

    <!--magnific popup js-->
    <script src="assets/js/magnific-popup.js"></script>

    <!--owl js-->
    <script src="assets/js/owl.carousel.min.js"></script>

    <!-- Swiper JS -->
    <script src="assets/js/swiper.min.js"></script>

    <!-- AOS JS -->
    <script src="assets/js/aos.js"></script>

    <!-- MeanMenu JS -->
    <script src="assets/js/jquery.meanmenu.min.js"></script>

    <!-- TILT JS -->
    <script src="assets/js/vanilla-tilt.min.js"></script>

    <!-- script js-->
    <script src="assets/js/video-popup.js"></script>
    <script src="assets/js/script1.js"></script>
    <script src="assets/js/aos-init.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        var subscribeForm = document.getElementById('subscribe-form');
        $("#subscribe-form").click(function() {
            var email = document.getElementsByName("email")[0].value;
            if (validateEmail(email)) {
                $.post("api/email/api.cfc?method=Subscription", {
                        email: email
                    },
                    function(data) {
                        if (data.SUCCESS) {
                            $('#subscriptionModal').modal('show');
                        } else {
                            $('#subscriptionModalError').modal('show');
                        }
                    });
            } else {
                $('#subscriptionModalInValid').modal('show');
            }
        });

        function validateEmail(email) {
            const re =
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }
    </script>
    <script src="//code.tidio.co/5sfpvn4prhlq6uz8cnf4j2mfqahetgqw.js" async></script>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
      AOS.init();
    </script>
     <script>
        document.addEventListener('DOMContentLoaded', function() {
            const bespoke = document.getElementById('bespoke');
            bespoke.addEventListener('click', function(event) {
                console.log("Clicked on 'bespoke'");
                event.preventDefault();
                window.location.href = '../bespoke_solutions.html';
            });
        });
    </script>

    <script>
    const parallax = document.getElementById('parallax');
    window.addEventListener('scroll', function() {
        let offset = window.pageYOffset;
        parallax.style.backgroundPositionY = offset * 0.7 + 'px';
    })
</script>

</body>

</html>