<!DOCTYPE html>
<html lang="en">

<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="Gowell Solutions" name="description">
    <meta content="Gowell Solutions" name="keywords">
    <meta content="Gowell Solutions" name="author">

    <title>Gowell Solutions | Contact</title>

    <!-- Fav icon -->
    <link href="assets/images/logo/favicon.webp" rel="shortcut icon">
    <!-- Font Family-->
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
    <!--bootstrap css-->
    <link href="assets/css/bootstrap.css" rel="stylesheet" type="text/css">
    <!-- color css -->
    <link href="assets/css/inner-page.css" rel="stylesheet" type="text/css">
    <!--owl carousel css-->

    <!-- MeanMenu CSS -->
    <link rel="stylesheet" href="assets/css/meanmenu.min.css">

    <!-- AOS CSS -->
    <link href="assets/css/aos.css" rel="stylesheet">


    <!-- Icons -->
    <link href="assets/css/fontawesome.css" rel="stylesheet" type="text/css">
    <link href="assets/css/themify.css" rel="stylesheet" type="text/css">

    <!-- BoxIcons Min CSS -->
    <link rel="stylesheet" href="assets/css/boxicons.min.css">

    <!--magnific popup css-->
    <link href="assets/css/magnific-popup.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">

    <!--custom style-->
    <link href="assets/css/style.css" rel="stylesheet" type="text/css">
</head>

<body class="agency" data-offset="50" data-spy="scroll" data-target=".navbar">
    <!--loader start-->
    <div class="loader-wrapper">
        <div class="loader">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
    <!--loader end-->
    <!-- <headr data-include="common/header"></headr> -->


    <header class="app1 nav-abs custom-scroll">
        <div class="container">
            <div class="row">
                <div class="col">
                    <nav class="navbar navbar-expand-lg navbar-dark ">
                        <a class="d-inline-block m-r-auto" href="./">
                            <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                        </a>
    
                        <div class="collapse navbar-collapse mean-menu" id="navbarSupportedContent">
                            <a class="mean-menu-logo" href="./">
                                <img alt="" class="logo-header" src="assets/images/logo/Group <EMAIL>">
                            </a>
                            <ul class="navbar-nav ml-auto">
                                <li class="nav-item">
                                    <a  class="nav-link" href="" onclick="Calendly.initPopupWidget({url: 'https://calendly.com/darren-sabet/gowell'});return false;">Schedule A Meeting</a>
                                </li>
    
                                <li class="nav-item">
                                    <a  class="nav-link" href="./">About Us</a>
                                </li>
    
                                <li class="nav-item" id="Solutions">
                                    <a  class="nav-link" href="#" onclick="toggleDropdown()">
                                        Solutions <i class='bx bx-chevron-down'></i>
                                    </a>
                                </li>
    
                                <li id="contact" class="nav-item">
                                    <a  class="nav-link" href="./contact.html">Contact Us</a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <div id="dropdownContent" style="display: none; position: absolute; top: 60px; right: 0;  width: 350px; z-index: 999; transition: ease-in-out;">
        <!-- Dropdown content provided in the question -->
        <div style="padding: 10px; margin-top: 10px;">
            <!-- Insert your dropdown content here -->
            <div style="width: 350px; height: fit-content; background-color: rgb(255, 255, 255); display: flex;  flex-direction: column; color: white;">
                <div style="padding: 10px;">
                  
                        <ul style="display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">
                                <a style="font-size: 20px; color: #1D456B;" href="./bespoke_solutions.html">Bespoke Solutions</a>
                            </p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/agriculture.webp" style="width: 40px; height: 40px;"/>
                                <p> <a href="./arcn.html"> Agricultural Research MIS Solution</a>
                                </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/instacare_logo.webp" style="width: 40px; height: 40px; "/>
                                <p><a href="./instacare.html">instaCare</a>
                                    </p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/enterprise_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./Emsplus.html">Enterprise Management System Plus (EMS Plus)</a></p>
                            </div>
                        </ul>
                  
               
                        <ul style="margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Commercial Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/edana_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edana.html"> Edana.co</a>
                                   </p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/ed-admin_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./edadmin.html">Ed-admin</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px;">
                                <img src="./assets/headstart_logo.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./headstart_academy.html">Headstart Academy</a></p>
                            </div>
                        </ul>
                        <ul  style=" margin-top: 20px; display: flex; justify-content: center;  flex-direction: column; list-style: none;">
                            <p style="font-size: 20px; color: #1D456B;">Social Responsibility Solutions</p>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img src="https://play-lh.googleusercontent.com/__4aC117XQcBDEC7AZX5d_mGY5Hyiw3PCWJYXLgaVR_JM0zAZ6LONFUXNNT8xodJkk0=w480-h960-rw" style="width: 30px; height: 30px; border-radius: 50%;"/>
                                <p><a href="./wordsinspire.html">Words to Inspire App</a></p>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/4RmFML5nmtXAE1tS7RouxEoKgpnWLA7AFSAznCdeJkof8FnEKU655iRw-qS2j1Slwbw=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_life.html">Living a Baha’i Life App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 7px;">
                                <img style="width: 30px; height: 30px; border-radius: 50%;" src="https://play-lh.googleusercontent.com/JNIj29ffdxFlCP9U1vHS-MHpO7t7wJn2LBC4-5VW1wJwn4PRUCTLr2n82U10meGBI-Q=w480-h960-rw" style="width: 40px; height: 40px;"/>
                                <p><a href="./bahai_beats.html">Baha’i Beats App</a></p>
                            </div>
                            <div style="display: flex;  align-items: center; gap: 10px; padding: 5px; margin-left: 3px;">
                                <img src="./assets/B_lex.webp" style="width: 40px; height: 40px;"/>
                                <p><a href="./B_Lex.html">B.Lex App</a></p>
                            </div>
                        </ul>
                    
                </div>
                
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown() {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
   
       if (dropdownContent.style.display === "none") {
           dropdownContent.style.display = "block";
           document.addEventListener("click", closeDropdownOutside);
       } else {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
   
   function closeDropdownOutside(event) {
       var dropdownContent = document.getElementById("dropdownContent");
       var dropdownButton = document.getElementById("Solutions");
       var isClickInsideContent = dropdownContent.contains(event.target);
       var isClickInsideButton = dropdownButton.contains(event.target);
   
       if (!isClickInsideContent && !isClickInsideButton) {
           dropdownContent.style.display = "none";
           document.removeEventListener("click", closeDropdownOutside);
       }
   }
       </script>


    <span data-menuid="contact" class="d-none"></span>

    <!--megamenu section Start-->
    <div class="loding-header pt-5 pb-5">

    </div>
    <!--megamenu section end-->
    <!-- section start -->



    <section class="checkout-page format gym p-50">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div>
                        <div class="checkout-form">
                            <form id="contactForm" method="post">
                                <div class="row">
                                    <div class="col-lg-12 col-sm-12 col-xs-12">
                                        <div class="left-text">
                                            <div class="format-head-text">
                                                <h3 class="about-font-header">Contact Us</h3>

                                            </div>
                                            <div class="format-sub-text mb-0">
                                                <p>We’re open to discussing your ideas and look forward to bringing them to life.</p>
                                                <p>For FREE project consultations and estimates, simply contact us with the form below and an expert will get in touch with you as soon as possible.</p>
                                            </div>
                                            <br>
                                            <div class="row check-out">
                                                <div class="form-group col-sm-6 col-xs-12">
                                                    <div class="field-label">Name</div>
                                                    <input name="name" placeholder="" type="text" value="" class="field-bg">
                                                </div>
                                                <div class="form-group col-sm-6 col-xs-12">
                                                    <div class="field-label">Organisation</div>
                                                    <input name="organisation" placeholder="" type="text" value="" class="field-bg">
                                                </div>
                                                <div class="form-group col-sm-6 col-xs-12">
                                                    <div class="field-label">Country</div>
                                                    <input name="country" placeholder="" type="text" value="" class="field-bg">
                                                </div>
                                                <div class="form-group col-sm-6 col-xs-12">
                                                    <div class="field-label">Message</div>
                                                    <input name="message" placeholder="" type="text" value="" class="field-bg">
                                                </div>
                                                <div class="form-group col-sm-6 col-xs-12">
                                                    <div class="field-label">Email</div>
                                                    <input name="email" placeholder="" type="email" required data-error="Please enter your email" value="" class="field-bg">
                                                    <div class="help-block with-errors"></div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <button type="submit" class="btn btn-default primary-btn radius-0 mt-20 mb-20"><i class='bx bxs-paper-plane' style="color: white;"></i>Send Message<span></span></button>
                                                <div id="msgSubmit" class="h5 text-center hidden"></div>
                                                <div class="clearfix"></div>
                                            </div>

                                        </div>
                                    </div>
                            </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </section>
    <!-- Section ends -->

    <!-- footer start -->
    <!-- <footer class="app2 bg footer2 p-0 inner">
        <section>
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 text-center">
                        <div class="logo-sec">
                            <div class="footer-contant">
                                <img alt="" class="logo-footer" src="../assets/images/logo/gowell-solutions.webp">
                                <div class="footer-para">
                                    <p class="text-white">Instacare aims at helping healthcare professionals monitor or diagnose patients easily and quickly using mobile devices.</p>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
    </footer> -->
    <!-- footer end -->

    <!-- copyright start -->
    <div class="agency copyright inner-page">
        <div class="container">
            <div class="row">
                <div class="col-sm-6">
                    <div class="link-horizontal">
                        <ul>
                            <li><a class="copyright-text" href="./contact.html">Contact</a></li>
                            <li><a class="copyright-text" href="./privacypolicy.html">Privacy Policy</a></li>
                            <li><a class="copyright-text" href="./termsofuse.html">Terms of Us</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div>
                        <h6 id="copyright" class="copyright-text text-white text-right">Copyright © <span id="currentYear"></span> Gowell Solutions</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById("currentYear").innerText = new Date().getFullYear();
    </script>
    
    <!-- copyright end -->

    <!-- Tap on Top-->
    <div class="tap-top">
        <div><i class="fa fa-angle-double-up"></i></div>
    </div>
    <!-- Tap on Ends-->

    <!-- latest jquery-->
    <script src="assets/js/jquery-3.3.1.min.js"></script>
    <!-- popper js-->
    <script src="assets/js/popper.min.js"></script>
    <!-- Bootstrap js-->
    <script src="assets/js/bootstrap.js"></script>
    <!--magnific popup js-->
    <script src="assets/js/magnific-popup.js"></script>
    <!--  costamizer option -->
    <!--owl js-->
    <script src="assets/js/owl.carousel.min.js"></script>
    <!-- script js-->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/testimonial.js"></script>
    <script src="assets/js/video-popup.js"></script>
    <script src="assets/js/aos.js"></script>
    <script src="assets/js/aos-init.js"></script>
    <!-- MeanMenu JS -->
    <script src="assets/js/jquery.meanmenu.min.js"></script>

    <script src="assets/js/form-validator.min.js"></script>

    <!-- Contact Form Min JS -->
    <script src="assets/js/contact-form-script.js"></script>
   
</body>

</html>