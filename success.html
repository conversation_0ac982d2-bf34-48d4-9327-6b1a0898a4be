<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Thank You!</h1>
        <p>We appreciate your submission.</p>
        <p>We will get back to you as soon as we can</p>
        <p>Redirecting to home page in <span id="countdown">8</span> seconds...</p>
    </div>
    <script>
        // Countdown timer
        let countdownTimer = 8;
        const countdownElement = document.getElementById('countdown');

        const countdownInterval = setInterval(() => {
            countdownTimer--;
            countdownElement.textContent = countdownTimer;

            if (countdownTimer === 0) {
                clearInterval(countdownInterval);
                // Redirect to home page
                window.location.href = '/'; // Replace '/' with your home page URL
            }
        }, 1000);
    </script>
</body>



<style>
    /* Import a Google Font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');

/* Reset some default styles */
body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    background-color: #f4f4f4;
    color: #333;
}

/* Container styles */
.container {
    max-width: 600px;
    margin: 100px auto;
    padding: 40px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

/* Heading styles */
h1 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #294b65;
}

/* Paragraph styles */
p {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
}
</style>
</html>