image: alpine:latest

stages:
  - deploy

variables:
  URL: "gowell.solutions"

before_script:
  - 'which ssh-agent || ( apk add openssh-client git rsync )'
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan ${URL} >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts

deploy:
  stage: deploy
  script:
    - rsync -avc . ubuntu@${URL}:/var/www/${URL}/ --delete --exclude=".*"
    - ssh ubuntu@${URL} "find /var/www/${URL} -type f -perm 666 -exec chmod 644 {} \; && find /var/www/${URL} -type d -perm 777 -exec chmod 755 {} \;"

